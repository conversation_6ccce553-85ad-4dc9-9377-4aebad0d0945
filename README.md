# 遥感图像分类项目

本项目用于对 PatternNet 数据集中的遥感图像进行分类，支持多种模型架构（包括 ResNet50、DenseNet201、ViT、Swin Transformer）以及模型压缩技术（剪枝、蒸馏、量化）。

## 项目结构

```
src/
├── api/                    # FastAPI应用模块
│   ├── main.py            # FastAPI应用入口
│   ├── config.py          # 配置管理
│   ├── routers/           # API路由
│   │   ├── models.py      # 模型管理API
│   │   └── prediction.py  # 预测API
│   ├── services/          # 业务逻辑服务层
│   │   ├── model_service.py      # 模型管理服务
│   │   ├── prediction_service.py # 预测服务
│   │   └── comparison_service.py # 比较服务
│   └── schemas/           # Pydantic数据模型
│       └── models.py      # API数据模型定义
├── static/                # 前端静态文件
│   ├── index.html         # 主页面
│   ├── css/style.css      # 样式文件
│   └── js/app.js          # 前端JavaScript逻辑
├── data/                  # 数据处理相关代码
├── models/                # 模型定义 (base_model.py等)
├── optimization/          # 模型压缩方法实现 (pruning.py, distillation.py, quantization.py)
├── utils/                 # 工具函数 (metrics.py, visualization.py)
├── train.py               # 训练辅助脚本 (train_model 函数)
├── evaluate.py            # 评估辅助脚本 (evaluate_model 函数)
├── prune.py               # 剪枝微调辅助脚本 (fine_tune 函数)
├── quanti.py              # 独立量化脚本
├── compare.py             # 模型参数比较脚本
├── main.py                # 主流程控制脚本 (自动化训练、评估、剪枝、蒸馏多个模型)
├── app.py                 # 原Gradio交互式演示界面（已重构为FastAPI）
└── requirements.txt       # Python依赖
run_api.py                 # FastAPI应用启动脚本
test_api.py                # API功能测试脚本
Dockerfile                 # Docker镜像构建文件
.dockerignore              # Docker忽略文件
README.md                  # 项目说明
DOCKER.md                  # Docker部署指南
imgs/                      # 上传图像和临时文件目录
outputs/                   # 训练和评估的输出目录 (run_YYYYMMDD_HHMMSS 子目录)
├── run_YYYYMMDD_HHMMSS/
│   ├── global_args.txt       # 全局运行参数
│   ├── resnet50/             # ResNet50 的输出
│   │   ├── model.pth         # 训练后的模型
│   │   ├── evaluation_results.txt # 评估结果
│   │   ├── args.txt          # 训练参数
│   │   ├── training_curves.png # 训练曲线
│   │   ├── pruned/           # 剪枝结果
│   │   │   └── global_50/
│   │   │       ├── pruned_global_50.pth
│   │   │       └── evaluation_results.txt
│   │   └── distilled/        # 蒸馏结果
│   │       ├── student_mobilenetv2.pth
│   │       └── evaluation_results.txt
│   ├── densenet201/          # DenseNet201 的输出
│   │   └── ...
│   ├── vit_s_16/             # ViT-S/16 的输出
│   │   └── ...
│   └── swin_t/               # Swin-T 的输出
│       └── ...
└── ...
```

## 支持的模型

- ResNet50 (默认)
- DenseNet201
- ViT-S/16 (`vit_small_patch16_224` 来自 timm 库)
- Swin-T (`swin_tiny_patch4_window7_224` 来自 timm 库)
- MobileNetV2 (作为蒸馏的学生模型)
- ResNet18 (作为蒸馏的学生模型)

## 使用说明

**核心脚本: `main.py`**

`main.py` 现在是自动化的主要入口点，用于依次处理一系列模型的训练、评估和优化（剪枝、蒸馏）。

### 环境配置

```bash
# 建议使用conda创建虚拟环境
# conda create -n remote_sensing python=3.10
# conda activate remote_sensing

pip install -r src/requirements.txt
```

### 自动化训练与优化 (`main.py`)

运行 `main.py` 将按顺序处理 `--model_names` 列表中的每个模型。

**示例：训练、评估、剪枝(global 50%)、蒸馏(MobileNetV2) ResNet50 和 Swin-T**

```bash
python src/main.py \
    --model_names resnet50 swin_t \
    --data_dir PatternNet/images \
    --pretrained \
    --epochs 20 \
    --optimize_mode all \
    --pruning_methods global \
    --pruning_ratios 0.5 \
    --fine_tune_epochs 10 \
    --student_model mobilenetv2 \
    --distill_epochs 30 \
    --output_dir outputs
```

**执行流程:**

1.  创建一个带时间戳的全局运行目录 (例如 `outputs/run_YYYYMMDD_HHMMSS`)。
2.  **对于 `resnet50`**：
    - 在 `outputs/run_.../resnet50/` 下训练模型。
    - 评估训练后的模型。
    - 如果 `optimize_mode` 包含 `prune`，执行剪枝 (结果在 `.../resnet50/pruned/`)。
    - 如果 `optimize_mode` 包含 `distill`，执行蒸馏 (结果在 `.../resnet50/distilled/`)。
    - 如果 `optimize_mode` 包含 `quantize`，打印运行 `quanti.py` 的命令。
3.  **对于 `swin_t`**：
    - 在 `outputs/run_.../swin_t/` 下训练模型。
    - 评估训练后的模型。
    - 如果 `optimize_mode` 包含 `prune`，执行剪枝 (结果在 `.../swin_t/pruned/`)。
    - 如果 `optimize_mode` 包含 `distill`，执行蒸馏 (结果在 `.../swin_t/distilled/`)。
    - 如果 `optimize_mode` 包含 `quantize`，打印运行 `quanti.py` 的命令。
4.  ... 对列表中的其他模型重复此过程。
5.  **最后**：如果 `optimize_mode` 包含 `quantize`，在所有模型处理完毕后，打印用于对整个运行目录进行**批量静态量化**的 `quanti.py` 命令提示。

**关键参数:**

- `--model_names`: 要处理的模型名称列表 (空格分隔，默认: `resnet50 densenet201 vit_s_16 swin_t`)。
- `--pretrained`: 是否使用 ImageNet 预训练权重开始训练。
- `--epochs`: 每个模型的训练轮数。
- `--output_dir`: 保存所有运行结果的基础目录。
- `--optimize_mode`: 选择要执行的优化步骤 ('none', 'prune', 'distill', 'quantize'(仅在最后提示批量命令), 'all'(除量化外都执行))。
- **剪枝参数**: `--pruning_methods`, `--pruning_ratios`, `--fine_tune_epochs`, `--fine_tune_lr`。
- **蒸馏参数**: `--student_model`, `--temperature`, `--alpha`, `--distill_epochs`, `--distill_lr`。

### 静态量化 (独立脚本 `quanti.py`)

`quanti.py` 支持两种模式：

1.  **批量模式 (推荐)**: 在 `main.py` 运行完成后，如果需要对该次运行的所有模型进行量化，根据 `main.py` 输出的提示运行 `quanti.py`，使用 `--run_directory` 指定包含所有模型子目录的全局运行目录。脚本会自动查找每个子目录下的 `model.pth` 进行量化。
2.  **单模型模式**: 对单个模型进行量化，需要指定 `--model_name` 和 `--model_path`。

**示例 (批量模式，基于 `main.py` 的输出提示):**

```bash
python src/quanti.py \
    --run_directory outputs/run_YYYYMMDD_HHMMSS \
    --data_dir PatternNet/images \
    --calibration_batches 10 # 可选，默认50
    # 输出会自动保存在每个模型目录下的 quantized 子目录中
```

**示例 (单模型模式):**

```bash
python src/quanti.py \
    --model_name resnet50 \
    --model_path outputs/run_YYYYMMDD_HHMMSS/resnet50/model.pth \
    --data_dir PatternNet/images \
    --calibration_batches 10 \
    --output_dir outputs/run_YYYYMMDD_HHMMSS/resnet50/quantized # 明确指定输出目录
```

在批量模式下，量化结果（模型、评估、大小比较）将自动保存在各自模型目录下的 `quantized` 子目录中 (例如 `outputs/run_.../resnet50/quantized/`, `outputs/run_.../swin_t/quantized/` 等)。在单模型模式下，结果保存在 `--output_dir` 指定的路径。

### 模型比较 (独立脚本 `compare.py`)

使用 `compare.py` 比较两个模型（例如，原始模型和剪枝后的模型）的参数量和稀疏度。

**示例:**

```bash
python src/compare.py \
    --original_model_path outputs/run_YYYYMMDD_HHMMSS/resnet50/model.pth \
    --original_model_name resnet50 \
    --optimized_model_path outputs/run_YYYYMMDD_HHMMSS/resnet50/pruned/global_50/pruned_global_50.pth \
    --optimized_model_name resnet50 \
    --output_file outputs/run_YYYYMMDD_HHMMSS/resnet50/pruned/global_50/compare.txt
```

### Web 应用界面

项目提供了基于 FastAPI 的现代化 Web 应用，支持 RESTful API 和交互式前端界面。

#### 🚀 快速启动

**方式一：直接运行**

```bash
# 安装依赖
pip install -r requirements.txt

# 启动FastAPI应用
python run_api.py
```

**方式二：使用 Docker**

```bash
# 构建镜像
docker build -t remote-sensing-api .

# 运行容器
docker run -p 8000:8000 -v $(pwd)/outputs:/app/outputs:ro -v $(pwd)/imgs:/app/imgs remote-sensing-api
```

#### 🌐 访问地址

启动后可通过以下地址访问：

- **前端页面**: http://localhost:8000 - 交互式 Web 界面，支持图像预测和模型比较
- **API 文档**: http://localhost:8000/docs (Swagger UI)
- **API 文档**: http://localhost:8000/redoc (ReDoc)
- **健康检查**: http://localhost:8000/health

#### 🖥️ 前端页面使用说明

访问 http://localhost:8000 可以使用交互式 Web 界面，包含以下功能：

**🔍 图像预测功能**：

1. **选择模型**：从下拉菜单中选择要使用的模型（ResNet50、DenseNet201、ViT-S/16、Swin-T 及其优化版本）
2. **上传图像**：
   - 点击上传区域选择图像文件
   - 或直接拖拽图像文件到上传区域
   - 支持 JPG、PNG、GIF 格式，最大 10MB
3. **查看预览**：上传后可以预览图像
4. **开始预测**：点击"开始预测"按钮获取分类结果
5. **查看结果**：显示 Top-5 预测结果，包含类别名称、置信度和推理时间

**📊 模型比较功能**：

1. **选择模型类型**：选择要比较的基础模型（ResNet50、DenseNet201、Swin-T、ViT-S/16）
2. **选择比较类型**：选择比较方式（剪枝比较、蒸馏比较、量化比较）
3. **生成报告**：点击"生成比较报告"查看详细的性能对比数据
4. **查看对比**：显示原始模型与优化模型的性能指标对比表格

**💡 使用提示**：

- 界面采用响应式设计，支持桌面和移动设备访问
- 预测过程中会显示加载动画和进度提示
- 所有操作都有实时反馈和错误提示
- 支持多种模型的性能对比分析

#### 🔌 API 接口

**核心端点**：

```bash
# 获取可用模型列表
GET /api/models/

# 图像预测
POST /api/predict/
Content-Type: multipart/form-data
Body: file=<image_file>&model_key=<model_name>

# 模型比较
GET /api/models/{model_type}/comparison/{comparison_type}

# 系统信息
GET /api/info

# 健康检查
GET /health
```

**API 使用示例**：

```python
import requests

# 预测图像
with open('image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/predict/',
        files={'file': f},
        data={'model_key': 'resnet50-原始'}
    )
    result = response.json()
    print(result['results'])  # 预测结果

# 获取模型比较
response = requests.get(
    'http://localhost:8000/api/models/resnet50/comparison/pruning'
)
comparison = response.json()
```

详细的 API 文档请访问：http://localhost:8000/docs

#### 🐳 Docker 部署

项目提供完整的 Docker 化部署方案：

```bash
# 基本部署
docker build -t remote-sensing-api .
docker run -p 8000:8000 remote-sensing-api

# GPU支持部署
docker run --gpus all -p 8000:8000 remote-sensing-api

# 挂载数据目录
docker run -p 8000:8000 \
  -v $(pwd)/outputs:/app/outputs:ro \
  -v $(pwd)/imgs:/app/imgs \
  remote-sensing-api
```

更多 Docker 部署选项请参考 [DOCKER.md](DOCKER.md)

#### 📱 兼容性说明

- **浏览器支持**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **移动端**: 支持 iOS Safari 和 Android Chrome
- **API 兼容**: 遵循 OpenAPI 3.0 标准，支持各种 HTTP 客户端

## 🎯 快速启动指南

### 启动方式

```bash
# 方式一：直接运行（需要Python环境）
pip install -r requirements.txt
python run_api.py

# 方式二：Docker运行（推荐）
docker build -t remote-sensing-api .
docker run -p 8000:8000 remote-sensing-api
```

### 访问应用

启动成功后，访问以下地址：

- **前端页面**: http://localhost:8000 - 主要的交互式界面
- **API 文档**: http://localhost:8000/docs - 完整的 API 文档

## 未来工作与改进

- 改进 `optimization/quantization.py` 以更好地支持 ViT/Swin 等 Transformer 模型的静态量化（可能需要第三方库）。
- 考虑为 ViT/Swin 添加动态量化选项。
- 改进 `optimization/pruning.py` 中的 `get_prunable_layers` 以更精确地识别 Transformer 中的可剪枝层。
- 将 `compare.py` 的功能集成到 `main.py` 或 `evaluate.py` 中，自动生成比较文件。
- 添加更多遥感图像特定的数据增强方法。
- 优化 `app.py` 中模型发现和信息加载的逻辑，使其更具鲁棒性，能处理多个运行目录。

## 注意事项

1.  静态量化目前主要对 ResNet 类模型效果较好，ViT/Swin 可能不适用。
2.  剪枝 Transformer 模型可能需要特定于其结构的方法。
3.  知识蒸馏相对通用，可以尝试不同的教师/学生组合。
4.  训练多个模型和优化过程可能需要较长时间和大量计算资源。

## 训练与微调建议

- **强烈建议对 ViT 和 Swin Transformer 等基于 Transformer 的模型使用 ImageNet 预训练权重进行微调 (`--pretrained` 标志，现在默认为 True)。** 在 PatternNet 这样相对较小的数据集上从头训练这些模型通常效果不佳。
- **微调超参数**: 当使用预训练权重进行微调时，建议：
  - 使用较小的学习率，例如 `--learning_rate 1e-4` 或 `1e-5`。
  - 使用较少的训练轮数，例如 `--epochs 20` 到 `50`，具体取决于验证集性能。
  - 可以考虑在 `train.py` 中添加学习率调度器（如带预热的余弦退火）以获得更好的微调效果。
- **从头训练**: 如果确实需要从头训练（不使用 `--pretrained`，即运行时添加 `--no-pretrained`），请确保有足够的数据，并准备进行更长时间的训练和更仔细的超参数调整。

## 性能优化建议

- 剪枝后进行微调通常能显著恢复精度。
- 蒸馏时调整温度和 alpha 参数可以平衡精度和学生模型性能。
- 使用 Gradio 的懒加载模式可以有效减少演示时的内存占用。

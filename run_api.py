#!/usr/bin/env python3
"""
FastAPI应用启动脚本

使用方法:
    python run_api.py
    
环境变量:
    HOST: 服务器主机地址 (默认: 0.0.0.0)
    PORT: 服务器端口 (默认: 8000)
    DEBUG: 调试模式 (默认: false)
"""

import os
import sys
import uvicorn

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.api.config import settings

if __name__ == "__main__":
    print("🚀 启动遥感图像分类API服务...")
    print(f"📍 服务地址: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔧 调试模式: {'开启' if settings.DEBUG else '关闭'}")
    print(f"💻 计算设备: {settings.get_device()}")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "api.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

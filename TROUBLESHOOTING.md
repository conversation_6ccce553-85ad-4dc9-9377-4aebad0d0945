# 故障排除指南

本文档提供遥感图像分类系统常见问题的诊断和解决方案，帮助快速定位和修复问题。

## 📋 目录

- [快速诊断](#快速诊断)
- [启动问题](#启动问题)
- [API 问题](#api问题)
- [模型问题](#模型问题)
- [性能问题](#性能问题)
- [Docker 问题](#docker问题)
- [网络问题](#网络问题)
- [日志分析](#日志分析)

## 🔍 快速诊断

### 系统健康检查

```bash
# 1. 检查服务状态
curl http://localhost:8000/health

# 2. 检查系统信息
curl http://localhost:8000/api/info

# 3. 检查可用模型
curl http://localhost:8000/api/models/

# 4. 检查进程状态
ps aux | grep python
ps aux | grep uvicorn

# 5. 检查端口占用
netstat -tlnp | grep 8000
lsof -i :8000
```

### 快速修复命令

```bash
# 重启服务
sudo systemctl restart remote-sensing-api

# 重启Docker容器
docker restart remote-sensing-api

# 重新构建Docker镜像
docker build --no-cache -t remote-sensing-api .

# 清理缓存
rm -rf __pycache__/
rm -rf .pytest_cache/

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

## 🚀 启动问题

### 问题 1：服务无法启动

**症状：**

```
ModuleNotFoundError: No module named 'fastapi'
```

**解决方案：**

```bash
# 检查Python环境
python --version
which python

# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import fastapi; print('FastAPI installed')"
```

### 问题 2：端口被占用

**症状：**

```
OSError: [Errno 98] Address already in use
```

**解决方案：**

```bash
# 查找占用端口的进程
lsof -i :8000
netstat -tlnp | grep 8000

# 杀死占用进程
kill -9 <PID>

# 或使用不同端口
PORT=8001 python run_api.py
```

### 问题 3：权限问题

**症状：**

```
PermissionError: [Errno 13] Permission denied
```

**解决方案：**

```bash
# 检查文件权限
ls -la run_api.py
ls -la src/

# 修复权限
chmod +x run_api.py
chmod -R 755 src/

# 检查目录权限
mkdir -p imgs logs
chmod 755 imgs logs
```

### 问题 4：环境变量问题

**症状：**

```
KeyError: 'MODEL_BASE_DIR'
```

**解决方案：**

```bash
# 创建环境变量文件
cp .env.example .env

# 编辑配置
nano .env

# 验证环境变量
python -c "import os; print(os.getenv('MODEL_BASE_DIR', 'Not set'))"
```

## 🔌 API 问题

### 问题 1：API 响应 404

**症状：**

```json
{ "detail": "Not Found" }
```

**诊断：**

```bash
# 检查API路由
curl -v http://localhost:8000/api/models/

# 检查服务状态
curl http://localhost:8000/health
```

**解决方案：**

```bash
# 检查API前缀配置
grep API_PREFIX .env

# 验证路由注册
python -c "
from src.api.main import app
for route in app.routes:
    print(route.path)
"
```

### 问题 2：CORS 错误

**症状：**

```
Access to fetch at 'http://localhost:8000/api/models/' from origin 'http://localhost:3000' has been blocked by CORS policy
```

**解决方案：**

```bash
# 更新CORS配置
echo "CORS_ORIGINS=http://localhost:3000,http://localhost:8080" >> .env

# 或允许所有来源（仅开发环境）
echo "CORS_ORIGINS=*" >> .env

# 重启服务
```

### 问题 3：文件上传失败

**症状：**

```json
{ "success": false, "error": "文件大小超过10MB限制" }
```

**解决方案：**

```bash
# 增加上传限制
echo "MAX_UPLOAD_SIZE=20971520" >> .env  # 20MB

# 检查Nginx配置（如果使用）
sudo nano /etc/nginx/sites-available/remote-sensing-api
# 添加: client_max_body_size 20M;

# 重启服务
sudo systemctl reload nginx
```

## 🤖 模型问题

### 问题 1：模型加载失败

**症状：**

```
FileNotFoundError: [Errno 2] No such file or directory: 'outputs/run_xxx/model.pth'
```

**诊断：**

```bash
# 检查模型目录
ls -la outputs/
find outputs/ -name "*.pth" -type f

# 检查模型路径配置
grep MODEL_BASE_DIR .env
```

**解决方案：**

```bash
# 创建测试模型目录
mkdir -p outputs/run_$(date +%Y%m%d_%H%M%S)/resnet50

# 或更新模型路径
echo "MODEL_BASE_DIR=/path/to/your/models" >> .env

# 检查模型文件权限
chmod -R 644 outputs/
```

### 问题 2：GPU 不可用

**症状：**

```
RuntimeError: CUDA out of memory
```

**诊断：**

```bash
# 检查GPU状态
nvidia-smi

# 检查CUDA可用性
python -c "import torch; print(torch.cuda.is_available())"

# 检查GPU内存使用
nvidia-smi --query-gpu=memory.used,memory.total --format=csv
```

**解决方案：**

```bash
# 强制使用CPU
echo "DEVICE=cpu" >> .env

# 清理GPU内存
python -c "import torch; torch.cuda.empty_cache()"

# 减少模型缓存
echo "MODEL_CACHE_SIZE=1" >> .env

# 重启服务
```

### 问题 3：模型预测错误

**症状：**

```json
{ "success": false, "error": "预测失败: tensor size mismatch" }
```

**解决方案：**

```bash
# 检查图像预处理
python -c "
from PIL import Image
from src.api.services.prediction_service import PredictionService
img = Image.open('test_image.jpg')
print(f'Image size: {img.size}, mode: {img.mode}')
"

# 验证模型兼容性
python -c "
import torch
model = torch.load('path/to/model.pth', map_location='cpu')
print(f'Model type: {type(model)}')
"
```

## ⚡ 性能问题

### 问题 1：响应时间过长

**症状：**
API 响应时间超过 30 秒

**诊断：**

```bash
# 测试API响应时间
time curl http://localhost:8000/api/models/

# 检查系统资源
htop
iotop
```

**解决方案：**

```bash
# 启用模型缓存
echo "MODEL_CACHE_SIZE=3" >> .env

# 使用多worker
uvicorn src.api.main:app --workers 4 --host 0.0.0.0 --port 8000

# 优化图像预处理
# 在代码中添加图像大小限制
```

### 问题 2：内存使用过高

**症状：**

```
MemoryError: Unable to allocate array
```

**解决方案：**

```bash
# 监控内存使用
free -h
ps aux --sort=-%mem | head

# 增加swap空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 限制模型缓存
echo "MODEL_CACHE_SIZE=1" >> .env
```

### 问题 3：CPU 使用率过高

**解决方案：**

```bash
# 限制worker数量
uvicorn src.api.main:app --workers 2

# 使用进程池
# 在代码中实现异步处理

# 优化模型推理
echo "DEVICE=cuda" >> .env  # 如果有GPU
```

## 🐳 Docker 问题

### 问题 1：镜像构建失败

**症状：**

```
ERROR: failed to solve: process "/bin/sh -c pip install -r requirements.txt" did not complete successfully
```

**解决方案：**

```bash
# 清理Docker缓存
docker system prune -a

# 无缓存构建
docker build --no-cache -t remote-sensing-api .

# 检查网络连接
docker run --rm alpine ping -c 3 pypi.org

# 使用国内镜像源
# 在Dockerfile中添加：
# RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
```

### 问题 2：容器启动失败

**症状：**

```
docker: Error response from daemon: driver failed programming external connectivity
```

**解决方案：**

```bash
# 检查端口冲突
docker ps -a
netstat -tlnp | grep 8000

# 使用不同端口
docker run -p 8001:8000 remote-sensing-api

# 重启Docker服务
sudo systemctl restart docker
```

### 问题 3：容器内文件权限问题

**症状：**

```
PermissionError: [Errno 13] Permission denied: '/app/outputs'
```

**解决方案：**

```bash
# 检查容器内权限
docker exec -it remote-sensing-api ls -la /app/

# 修复权限
docker exec -it remote-sensing-api chown -R appuser:appuser /app/

# 重新构建镜像
docker build -t remote-sensing-api .
```

## 🌐 网络问题

### 问题 1：无法访问 API

**症状：**

```
curl: (7) Failed to connect to localhost port 8000: Connection refused
```

**诊断：**

```bash
# 检查服务状态
systemctl status remote-sensing-api
docker ps

# 检查端口监听
netstat -tlnp | grep 8000
ss -tlnp | grep 8000

# 检查防火墙
sudo ufw status
sudo iptables -L
```

**解决方案：**

```bash
# 开放端口
sudo ufw allow 8000

# 检查绑定地址
grep HOST .env
# 确保设置为 HOST=0.0.0.0

# 重启服务
sudo systemctl restart remote-sensing-api
```

### 问题 2：代理配置问题

**症状：**
Nginx 502 Bad Gateway

**解决方案：**

```bash
# 检查Nginx配置
sudo nginx -t

# 检查上游服务
curl http://127.0.0.1:8000/health

# 检查Nginx日志
sudo tail -f /var/log/nginx/error.log

# 重启Nginx
sudo systemctl restart nginx
```

## 📊 日志分析

### 应用日志

```bash
# 查看应用日志
tail -f logs/app.log

# 查看系统服务日志
sudo journalctl -u remote-sensing-api -f

# 查看Docker容器日志
docker logs -f remote-sensing-api
```

### 常见日志错误

**错误 1：模块导入失败**

```
ModuleNotFoundError: No module named 'src'
```

解决：检查 PYTHONPATH 和工作目录

**错误 2：数据库连接失败**

```
ConnectionError: Unable to connect to database
```

解决：检查数据库服务状态和连接配置

**错误 3：内存不足**

```
MemoryError: Unable to allocate memory
```

解决：增加系统内存或优化内存使用

### 日志级别配置

```bash
# 开启调试日志
echo "LOG_LEVEL=DEBUG" >> .env

# 查看详细错误信息
echo "DEBUG=true" >> .env

# 重启服务查看详细日志
```

## 🛠️ 调试工具

### 1. 交互式调试

```python
# 在代码中添加断点
import pdb; pdb.set_trace()

# 或使用ipdb
import ipdb; ipdb.set_trace()
```

### 2. 性能分析

```python
# 使用cProfile
python -m cProfile -o profile.stats run_api.py

# 分析结果
python -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(10)
"
```

### 3. 内存分析

```python
# 使用memory_profiler
pip install memory-profiler
python -m memory_profiler run_api.py
```

## 📞 获取帮助

### 收集诊断信息

```bash
#!/bin/bash
# 诊断信息收集脚本
echo "=== 系统信息 ===" > diagnosis.txt
uname -a >> diagnosis.txt
python --version >> diagnosis.txt
docker --version >> diagnosis.txt

echo "=== 服务状态 ===" >> diagnosis.txt
systemctl status remote-sensing-api >> diagnosis.txt
docker ps >> diagnosis.txt

echo "=== 网络状态 ===" >> diagnosis.txt
netstat -tlnp | grep 8000 >> diagnosis.txt

echo "=== 日志 ===" >> diagnosis.txt
tail -50 logs/app.log >> diagnosis.txt

echo "诊断信息已保存到 diagnosis.txt"
```

### 联系支持

在报告问题时，请提供：

1. 错误症状和复现步骤
2. 系统环境信息
3. 相关日志输出
4. 已尝试的解决方案

---

**提示**: 大多数问题可以通过重启服务、检查配置和查看日志来解决。如果问题持续存在，请参考项目文档或联系技术支持。
